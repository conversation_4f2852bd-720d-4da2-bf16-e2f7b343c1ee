"use client";
import React, {
  useContext,
  useMemo,
  useCallback,
  useRef,
  useState,
  useEffect,
} from "react";
import { SeekPlayer } from "./SeekPlayer";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import { Box, styled, useTheme } from "@mui/material";
import { TimeLineListByTrack } from "./TimeLineListByTrack";
import { TimeScaleMarkers } from "./TimeScaleMarkers";
import { DragDropOverlay } from "./DragDropOverlay";
import { getUid } from "../../utils";

import {
  TIMELINE_CONSTANTS,
  calculateTimelinePosition,
  getTimelineContainerWidth,
} from "../../utils/timeUtils";
import "./dragdrop.css";

export const TimeLine = observer(() => {
  const store = useContext(StoreContext);
  const theme = useTheme();
  const timelineContainerRef = useRef<HTMLDivElement | null>(null);
  const indicatorRef = useRef<HTMLDivElement | null>(null);
  const scrollbarTrackRef = useRef<HTMLDivElement | null>(null);
  const scrollbarThumbRef = useRef<HTMLDivElement | null>(null);
  const [isIndicatorDragging, setIsIndicatorDragging] = useState(false);
  const [isScrollbarDragging, setIsScrollbarDragging] = useState(false);
  const [initialMouseX, setInitialMouseX] = useState(0);
  const [initialTime, setInitialTime] = useState(0);
  const [initialScrollPosition, setInitialScrollPosition] = useState(0);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragData, setDragData] = useState<any>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [timelineRect, setTimelineRect] = useState<DOMRect | null>(null);
  const [dragPreviewTime, setDragPreviewTime] = useState(0);
  const [dragPreviewTrack, setDragPreviewTrack] = useState(0);

  // 使用状态来存储和更新容器宽度
  const [containerWidth, setContainerWidth] = useState<number | null>(null);

  // 使用ResizeObserver监听容器宽度变化
  useEffect(() => {
    // 初始化时获取一次容器宽度
    setContainerWidth(getTimelineContainerWidth());

    // 创建ResizeObserver监听容器宽度变化
    const observer = new ResizeObserver(() => {
      setContainerWidth(getTimelineContainerWidth());
    });

    // 获取容器元素并开始监听
    const container = document.querySelector(".timeline-container");
    if (container) {
      observer.observe(container);
    }

    // 清理函数
    return () => {
      if (container) {
        observer.unobserve(container);
      }
      observer.disconnect();
    };
  }, []);

  // 使用通用的位置计算函数来计算当前时间指示器的位置
  const adjustedPercentOfCurrentTime = useMemo(() => {
    // 如果容器宽度未知，返回0
    if (!containerWidth) return 0;

    return calculateTimelinePosition(
      store.currentTimeInMs,
      store.timelineDisplayDuration,
      store.timelinePan.offsetX,
      containerWidth // 传递容器宽度参数
    );
  }, [
    store.currentTimeInMs,
    store.timelinePan.offsetX,
    store.timelineDisplayDuration,
    containerWidth, // 添加containerWidth作为依赖项
  ]);

  // Calculate scrollbar position and size
  const scrollbarInfo = useMemo(() => {
    // Calculate the maximum offset allowed using the same logic as in the store
    const maxAllowedTime = Math.max(
      store.timelineDisplayDuration,
      store.maxTime
    );
    const maxOffset = Math.max(
      store.timelineDisplayDuration * 0.5,
      maxAllowedTime - store.timelineDisplayDuration
    );

    // Calculate the total scrollable range (from 0 to maxOffset)
    const totalScrollRange = maxOffset;

    // Calculate the current position as a percentage (0-100)
    // Map from [0, maxOffset] to [0, 100]
    // Positive offsetX means the timeline is panned left (showing later parts)
    const position = (store.timelinePan.offsetX / totalScrollRange) * 100;

    // Calculate thumb size based on visible range relative to total time
    const visibleRangeRatio = store.timelineDisplayDuration / maxAllowedTime;
    // Make sure thumb isn't too small
    const thumbSize = Math.max(10, visibleRangeRatio * 100);

    return { position, thumbSize, maxOffset, totalScrollRange };
  }, [store.timelinePan.offsetX, store.timelineDisplayDuration, store.maxTime]);

  const Indicator = styled(Box)(({ theme }) => ({
    position: "absolute",
    top: 0,
    left: 0,
    width: "2px",
    height: "100%",
    backgroundColor: theme.palette.primary.main,
    cursor: "ew-resize",
    borderRadius: "2px",
    zIndex: 1, // Local z-index within the container
    transition: "width 0.1s ease, background-color 0.1s ease",
    "&:hover": {
      width: "4px",
    },
  }));

  // Handle indicator dragging
  const handleIndicatorMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (e.button === 0) {
        // Left mouse button
        setIsIndicatorDragging(true);
        setInitialMouseX(e.clientX);
        setInitialTime(store.currentTimeInMs);
        e.stopPropagation(); // Prevent timeline panning
        e.preventDefault(); // Prevent other default behaviors
      }
    },
    [store]
  );

  const handleIndicatorMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isIndicatorDragging || !timelineContainerRef.current) return;

      const containerWidth = timelineContainerRef.current.clientWidth;
      const { HANDLE_WIDTH } = TIMELINE_CONSTANTS;

      // 调整有效内容区域宽度（减去左侧偏移）
      const effectiveWidth = containerWidth - HANDLE_WIDTH;

      const deltaX = e.clientX - initialMouseX;
      const deltaTime =
        (deltaX / effectiveWidth) * store.timelineDisplayDuration;

      // 计算新的时间，并限制在0到timelineDisplayDuration之间
      let newTime = Math.max(
        0,
        Math.min(store.timelineDisplayDuration, initialTime + deltaTime)
      );

      // 如果有元素，进一步限制不超过所有元素的最大endtime
      if (store.maxDuration > 0) {
        // 确保不超过最大元素endtime
        newTime = Math.min(newTime, store.maxDuration);
      }

      // Update the current time in the store
      store.handleSeek(newTime);

      e.preventDefault();
    },
    [isIndicatorDragging, initialMouseX, initialTime, store]
  );

  const handleIndicatorMouseUp = useCallback(
    (e: MouseEvent) => {
      if (isIndicatorDragging) {
        setIsIndicatorDragging(false);
        e.preventDefault();
      }
    },
    [isIndicatorDragging]
  );

  // 滚动条的拖拽处理
  const handleScrollbarMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (e.button === 0 && scrollbarThumbRef.current) {
        // 只处理左键点击
        setIsScrollbarDragging(true);
        setInitialMouseX(e.clientX);
        setInitialScrollPosition(store.timelinePan.offsetX);

        // 防止事件冒泡和默认行为
        e.stopPropagation();
        e.preventDefault();
      }
    },
    [store.timelinePan.offsetX]
  );

  const handleScrollbarTrackClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (
        e.button === 0 &&
        scrollbarTrackRef.current &&
        scrollbarThumbRef.current &&
        !isScrollbarDragging
      ) {
        // 计算点击位置相对于轨道的位置百分比
        const trackRect = scrollbarTrackRef.current.getBoundingClientRect();
        const clickPosition = (e.clientX - trackRect.left) / trackRect.width;

        // 根据点击位置计算新的偏移量
        const newOffset = clickPosition * scrollbarInfo.maxOffset;

        // 更新时间线偏移
        store.setTimelinePanOffset(newOffset);

        e.stopPropagation();
        e.preventDefault();
      }
    },
    [isScrollbarDragging, scrollbarInfo.maxOffset, store]
  );

  const handleScrollbarMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isScrollbarDragging || !scrollbarTrackRef.current) return;

      const trackRect = scrollbarTrackRef.current.getBoundingClientRect();
      const trackWidth = trackRect.width;

      // 计算鼠标移动的距离
      const deltaX = e.clientX - initialMouseX;

      // 将移动距离转换为时间线偏移量的变化
      const deltaRatio = deltaX / trackWidth;
      const deltaOffset = deltaRatio * scrollbarInfo.totalScrollRange;

      // 计算新的偏移量并限制在有效范围内
      const newOffset = Math.max(
        0,
        Math.min(scrollbarInfo.maxOffset, initialScrollPosition + deltaOffset)
      );

      // 更新时间线偏移
      store.setTimelinePanOffset(newOffset);

      e.preventDefault();
    },
    [
      isScrollbarDragging,
      initialMouseX,
      initialScrollPosition,
      scrollbarInfo.maxOffset,
      scrollbarInfo.totalScrollRange,
      store,
    ]
  );

  const handleScrollbarMouseUp = useCallback(() => {
    if (isScrollbarDragging) {
      setIsScrollbarDragging(false);
    }
  }, [isScrollbarDragging]);

  // Add and remove global event listeners for indicator dragging and scrollbar dragging
  useEffect(() => {
    window.addEventListener("mousemove", handleIndicatorMouseMove);
    window.addEventListener("mouseup", handleIndicatorMouseUp);
    window.addEventListener("mousemove", handleScrollbarMouseMove);
    window.addEventListener("mouseup", handleScrollbarMouseUp);

    return () => {
      window.removeEventListener("mousemove", handleIndicatorMouseMove);
      window.removeEventListener("mouseup", handleIndicatorMouseUp);
      window.removeEventListener("mousemove", handleScrollbarMouseMove);
      window.removeEventListener("mouseup", handleScrollbarMouseUp);
    };
  }, [
    handleIndicatorMouseMove,
    handleIndicatorMouseUp,
    handleScrollbarMouseMove,
    handleScrollbarMouseUp,
  ]);

  // Add non-passive wheel event listener to prevent browser back/forward navigation
  useEffect(() => {
    const container = timelineContainerRef.current;
    if (!container) return;

    // 使用 requestAnimationFrame 优化滚动事件处理
    let ticking = false;
    let lastWheelEvent: WheelEvent | null = null;

    const handleTimelineWheel = (e: WheelEvent) => {
      // 检测是否有水平滚动分量或是否按下了Ctrl/Shift键
      const hasHorizontalComponent = Math.abs(e.deltaX) > 0;
      const isCtrlPressed = e.ctrlKey || e.metaKey;
      const isShiftPressed = e.shiftKey;

      // 如果有水平滚动分量或按下了Ctrl/Shift键，阻止默认行为
      if (hasHorizontalComponent || isCtrlPressed || isShiftPressed) {
        e.preventDefault();
      }

      // 保存最新的事件
      lastWheelEvent = e;

      // 如果已经请求了动画帧，不再重复请求
      if (ticking) return;

      // 请求动画帧处理滚动
      ticking = true;
      requestAnimationFrame(() => {
        if (!lastWheelEvent) return;

        // 获取事件相对于容器的位置
        const rect = container.getBoundingClientRect();
        const clientX = lastWheelEvent.clientX - rect.left;

        // 检查是否在有效区域内（排除左侧10px的排序把手区域）
        const isInSortHandleArea = clientX < 10;

        // 如果在排序把手区域内，不处理滚动
        if (!isInSortHandleArea) {
          // 检测是否按下了Ctrl键（Mac上的Command键）
          const isCtrlPressed =
            lastWheelEvent.ctrlKey || lastWheelEvent.metaKey;

          // 如果是Ctrl+滚轮，执行缩放操作
          if (isCtrlPressed) {
            // 根据滚轮方向确定是放大还是缩小
            // deltaY < 0 表示向上滚动，放大；deltaY > 0 表示向下滚动，缩小
            if (lastWheelEvent.deltaY < 0) {
              // 放大时间线（减小显示的时间范围）
              const newDuration = Math.max(
                store.timelineDisplayDuration / 1.1,
                10 * 1000
              ); // 最小显示10秒
              store.setTimelineDisplayDuration(newDuration);
            } else {
              // 缩小时间线（增加显示的时间范围）
              const newDuration = Math.min(
                store.timelineDisplayDuration * 1.1,
                store.maxTime
              ); // 最大显示整个时间线
              store.setTimelineDisplayDuration(newDuration);
            }
          } else {
            // 处理普通滚动（水平滚动或Shift+垂直滚动）
            // 检测是否是水平滚动或Shift+垂直滚动
            const isHorizontalScroll =
              Math.abs(lastWheelEvent.deltaX) >
                Math.abs(lastWheelEvent.deltaY) || lastWheelEvent.shiftKey;

            if (isHorizontalScroll) {
              // 调用store的滚动处理方法
              const delta = lastWheelEvent.shiftKey
                ? lastWheelEvent.deltaY
                : lastWheelEvent.deltaX;
              store.handleTimelineWheel(delta);
            }
            // 对于纯垂直滚动，不需要额外处理，让浏览器自然处理
            // 这样就可以使用原生滚动条
          }
        }

        // 重置标志，允许处理下一个事件
        ticking = false;
        lastWheelEvent = null;
      });
    };

    // 使用原生事件监听器添加wheel事件，明确指定passive: false
    container.addEventListener("wheel", handleTimelineWheel, {
      passive: false,
    });

    return () => {
      container.removeEventListener("wheel", handleTimelineWheel);
    };
  }, [store]);

  const handleMouseLeave = useCallback(() => {
    if (store.timelinePan.isDragging) {
      store.endTimelinePan();
      // Reset cursor
      if (timelineContainerRef.current) {
        timelineContainerRef.current.style.cursor = "grab";
      }
    }
  }, [store]);

  const handleDoubleClick = useCallback(
    (e: React.MouseEvent) => {
      // 获取点击位置相对于容器的横坐标
      if (!timelineContainerRef.current) return;

      const rect = timelineContainerRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;

      // 如果点击在排序把手区域内，不执行重置操作
      if (clickX < 10) return;

      // 清除所有选中状态
      store.setSelectedElement(null);
      store.deselectAllCaptions();

      // Reset the timeline pan offset on double click
      store.resetTimelinePan();
    },
    [store]
  );

  // 处理拖拽进入
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();

    try {
      const data = JSON.parse(e.dataTransfer.getData("application/json"));
      setDragData(data);
    } catch (error) {
      // 如果无法解析数据，尝试从类型中获取
      const types = Array.from(e.dataTransfer.types);
      if (types.includes("application/json")) {
        // 数据将在drop事件中获取
        setDragData({ type: "unknown" });
      }
    }

    // 获取时间线容器的位置信息
    if (timelineContainerRef.current) {
      setTimelineRect(timelineContainerRef.current.getBoundingClientRect());
    }

    setIsDragOver(true);
  }, []);

  // 处理拖拽悬停
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";

    // 更新鼠标位置
    setMousePosition({ x: e.clientX, y: e.clientY });

    // 更新时间线位置信息（可能因为滚动而改变）
    if (timelineContainerRef.current) {
      setTimelineRect(timelineContainerRef.current.getBoundingClientRect());
    }
  }, []);

  // 处理拖拽离开
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    // 只有当拖拽真正离开时间线容器时才设置为false
    if (!timelineContainerRef.current?.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
      setDragData(null);
      setTimelineRect(null);
    }
  }, []);

  // 处理拖拽位置更新
  const handleDragPositionUpdate = useCallback(
    (time: number, trackIndex: number) => {
      setDragPreviewTime(time);
      setDragPreviewTrack(trackIndex);
    },
    []
  );

  // 处理拖拽放置
  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();

      // 计算精确的放置位置
      const rect = timelineContainerRef.current?.getBoundingClientRect();
      if (!rect) return;

      const dropX = e.clientX - rect.left;
      const dropY = e.clientY - rect.top;
      const { HANDLE_WIDTH } = TIMELINE_CONSTANTS;

      // 计算精确的时间位置
      const effectiveWidth = rect.width - HANDLE_WIDTH;
      const timePercent = Math.max(
        0,
        Math.min(1, (dropX - HANDLE_WIDTH) / effectiveWidth)
      );
      const visibleStartTime = store.timelinePan.offsetX;
      const dropTime =
        visibleStartTime + timePercent * store.timelineDisplayDuration;

      // 计算轨道索引
      const trackHeight = 40; // 38px轨道高度 + 2px边距 = 40px
      const topPadding = 35; // 顶部填充
      const trackIndex = Math.floor((dropY - topPadding) / trackHeight);

      // 清理拖拽状态
      setIsDragOver(false);
      setDragData(null);
      setTimelineRect(null);

      try {
        const dropData = JSON.parse(e.dataTransfer.getData("application/json"));

        if (dropData.type === "image") {
          // 创建图片元素
          const id = getUid();
          const imageElement = document.createElement("img");
          imageElement.src = dropData.src;
          imageElement.id = `image-${id}`;
          imageElement.style.display = "none";
          imageElement.crossOrigin = "anonymous";
          document.body.appendChild(imageElement);

          console.log(
            `Dropping image at time: ${dropTime}ms, track index: ${trackIndex}`
          );

          imageElement.onload = () => {
            // 添加图片元素到时间线
            store.addImageElement(imageElement, id, dropData.metadata);

            // 查找刚添加的元素
            const newElement = store.editorElements.find((el) => el.id === id);
            if (newElement) {
              // 设置元素的开始时间为拖拽位置
              const duration =
                newElement.timeFrame.end - newElement.timeFrame.start;
              store.updateEditorElementTimeFrame(
                newElement,
                {
                  start: dropTime,
                  end: dropTime + duration,
                },
                true
              );

              // 处理轨道分配
              const elementType = "image";
              const trackType = "media"; // 图片使用media轨道类型

              // 检查是否有足够的轨道
              const availableTracks = store.trackManager.tracks.filter(
                (track) => track.type === trackType
              );

              let targetTrack = null;

              if (trackIndex >= 0 && trackIndex < availableTracks.length) {
                // 拖拽到现有轨道
                targetTrack = availableTracks[trackIndex];
              } else if (trackIndex >= availableTracks.length) {
                // 拖拽到空轨道区域，创建新轨道
                const newTrackPosition = trackIndex;
                targetTrack = store.trackManager.createTrackAtPosition(
                  trackType,
                  newTrackPosition,
                  `Image Track ${
                    store.trackManager.getTrackCountByType(trackType) + 1
                  }`
                );
                console.log(
                  `Created new track at position ${newTrackPosition}`
                );
              }

              // 将元素移动到目标轨道
              if (targetTrack) {
                store.trackManager.moveElementToTrack(
                  newElement.id,
                  targetTrack.id
                );
                console.log(`Moved element to track: ${targetTrack.name}`);
              }

              // 选中新添加的元素
              store.setSelectedElement(newElement);

              console.log(`Image added successfully at time: ${dropTime}ms`);
            }
          };

          imageElement.onerror = () => {
            console.error("Failed to load dropped image");
            document.body.removeChild(imageElement);
          };
        }
      } catch (error) {
        console.error("Error handling drop:", error);
      }
    },
    [store]
  );

  // 注意：此函数已被原生wheel事件监听器替代
  // 保留此注释作为代码历史记录

  return (
    <Box
      sx={{
        height: "250px",
        width: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center", // Added for vertical centering
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "100%",
          mx: 1,
          justifyContent: "center",
          alignItems: "center",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <SeekPlayer />
        <Box
          ref={timelineContainerRef}
          className="timeline-container"
          sx={{
            position: "relative",
            overflowX: "hidden", // 隐藏水平滚动条
            overflowY: "auto", // 允许垂直滚动
            width: "100%",
            py: 1, // 减小内边距
            height: "calc(100% - 15px)", // 调整高度，留出更多空间给滚动条
            touchAction: "pan-y",
            WebkitOverflowScrolling: "touch",
            boxSizing: "border-box", // 确保padding和border包含在宽度内
            maxWidth: "100%", // 确保不超过父容器宽度
            display: "flex", // 使用flex布局
            flexDirection: "column", // 垂直方向排列子元素
            // 拖拽状态样式
            backgroundColor: isDragOver
              ? theme.palette.action.hover
              : "transparent",
            transition: "background-color 0.2s ease",
          }}
          onMouseLeave={handleMouseLeave}
          onDoubleClick={handleDoubleClick}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {/* Indicator positioned on top of everything - only shown when there are elements or captions */}
          {(store.editorElements.length > 0 || store.captions.length > 0) && (
            <Box
              position="relative"
              sx={{
                position: "absolute",
                left: `calc(${adjustedPercentOfCurrentTime}% + 10px)`, // 10px是排序把手宽度
                height: "100%", // Full height
                top: 0,
                zIndex: 50, // Higher than both TimeScaleMarkers and VirtualizedTimeline
                transform: "translateX(-50%)",
                cursor: "ew-resize",
                pointerEvents: "auto", // Ensure it can receive mouse events
              }}
              onMouseDown={handleIndicatorMouseDown}
            >
              <Indicator
                ref={indicatorRef}
                style={{
                  width: isIndicatorDragging ? "3px" : "2px",
                  backgroundColor: isIndicatorDragging
                    ? theme.palette.primary.dark
                    : theme.palette.primary.main,
                  transform: "translateX(-50%)",
                }}
              />
              {/* Indicator handle for better drag UX */}
              <Box
                sx={{
                  position: "absolute",
                  top: "0px", // Changed from "45px" to "0px" to position at the top
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: "10px",
                  height: "12px",

                  borderRadius: "0", // Changed from "50%" to "0" to make it square
                  backgroundColor: theme.palette.primary.main,
                  transition:
                    "transform 0.1s ease, box-shadow 0.1s ease, background-color 0.1s ease",
                  boxShadow: isIndicatorDragging
                    ? "0 0 8px rgba(0, 0, 0, 0.5)"
                    : "0 0 4px rgba(0, 0, 0, 0.3)",
                  zIndex: 2, // Ensure handle is above the line
                }}
              />
              {/* 倒三角形指向indicator */}
              <Box
                sx={{
                  position: "absolute",
                  top: "12px", // 就在方形handle下方
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: 0,
                  height: 0,
                  borderLeft: "5px solid transparent",
                  borderRight: "5px solid transparent",
                  borderTop: `6px solid ${theme.palette.primary.main}`,
                  zIndex: 2,
                }}
              />
            </Box>
          )}

          {/* Time scale markers */}
          <TimeScaleMarkers />

          {/* 使用按轨道分组的时间线列表 */}
          <TimeLineListByTrack />
        </Box>

        {/* 拖拽覆盖层 */}
        <DragDropOverlay
          isVisible={isDragOver}
          dragData={dragData}
          mousePosition={mousePosition}
          timelineRect={timelineRect}
          onPositionUpdate={handleDragPositionUpdate}
        />

        {/* 水平滚动条 */}
        <Box
          ref={scrollbarTrackRef}
          onClick={handleScrollbarTrackClick}
          sx={{
            position: "relative",
            width: "calc(100% - 20px)", // 保留与时间线相同宽度
            height: "10px",
            marginLeft: "10px", // 与时间线左侧对齐（排序把手宽度）

            borderRadius: "6px",
            cursor: "pointer",
          }}
        >
          <Box
            ref={scrollbarThumbRef}
            onMouseDown={handleScrollbarMouseDown}
            sx={{
              position: "absolute",
              left: `${scrollbarInfo.position}%`,
              width: "80px", // 固定宽度为80px
              height: "8px",
              top: "2px",
              backgroundColor:
                theme.palette.mode === "dark"
                  ? "rgba(255, 255, 255, 0.4)"
                  : "rgba(0, 0, 0, 0.3)",
              borderRadius: "4px",
              cursor: "grab",
              "&:hover": {
                backgroundColor:
                  theme.palette.mode === "dark"
                    ? "rgba(255, 255, 255, 0.6)"
                    : "rgba(0, 0, 0, 0.4)",
              },
              "&:active": {
                cursor: "grabbing",
                backgroundColor:
                  theme.palette.mode === "dark"
                    ? "rgba(255, 255, 255, 0.7)"
                    : "rgba(0, 0, 0, 0.5)",
              },
              transition: "background-color 0.2s ease",
            }}
          />
        </Box>
      </Box>
    </Box>
  );
});
