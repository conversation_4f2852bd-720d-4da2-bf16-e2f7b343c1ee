/* 拖拽状态样式 */
.dragging-media-item {
  cursor: grabbing !important;
}

.dragging-media-item * {
  cursor: grabbing !important;
}

/* 时间线拖拽区域样式 */
.timeline-container.drag-over {
  background-color: rgba(25, 118, 210, 0.05);
  transition: background-color 0.2s ease;
}

/* 拖拽预览动画 */
@keyframes dragPulse {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

.drag-preview-element {
  animation: dragPulse 1.5s ease-in-out infinite;
}

/* 插入指示器动画 */
@keyframes insertionGlow {
  0% {
    box-shadow: 0 0 5px rgba(25, 118, 210, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(25, 118, 210, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(25, 118, 210, 0.5);
  }
}

.insertion-indicator {
  animation: insertionGlow 2s ease-in-out infinite;
}

/* 轨道高亮动画 */
@keyframes trackHighlight {
  0% {
    background-color: rgba(25, 118, 210, 0.1);
    border-color: rgba(25, 118, 210, 0.3);
  }
  50% {
    background-color: rgba(25, 118, 210, 0.2);
    border-color: rgba(25, 118, 210, 0.6);
  }
  100% {
    background-color: rgba(25, 118, 210, 0.1);
    border-color: rgba(25, 118, 210, 0.3);
  }
}

.track-highlight {
  animation: trackHighlight 2s ease-in-out infinite;
}

/* 拖拽时禁用选择 */
.dragging-media-item,
.dragging-media-item * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 拖拽时的视觉反馈 */
.timeline-container.drag-active {
  position: relative;
}

.timeline-container.drag-active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 25%,
    rgba(25, 118, 210, 0.1) 25%,
    rgba(25, 118, 210, 0.1) 50%,
    transparent 50%,
    transparent 75%,
    rgba(25, 118, 210, 0.1) 75%
  );
  background-size: 20px 20px;
  animation: moveStripes 1s linear infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes moveStripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 20px;
  }
}

/* 媒体项拖拽样式 */
.media-item-dragging {
  opacity: 0.7;
  transform: scale(0.95);
  transition: all 0.2s ease;
}

/* 时间显示样式增强 */
.time-display {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(25, 118, 210, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .time-display {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(25, 118, 210, 0.5);
  }
  
  .timeline-container.drag-over {
    background-color: rgba(25, 118, 210, 0.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .drag-preview-element {
    font-size: 10px;
    height: 30px;
  }
  
  .time-display {
    font-size: 10px;
    padding: 2px 6px;
  }
}
