/* 时间线元素加载动画 */
@keyframes loading-shimmer {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40px 40px;
  }
}

.timeline-element-loading {
  animation: loading-shimmer 2s infinite linear;
}

/* 加载中的图片元素样式 */
.timeline-image-loading {
  background: linear-gradient(
    90deg,
    rgba(25, 118, 210, 0.1) 0%,
    rgba(25, 118, 210, 0.2) 50%,
    rgba(25, 118, 210, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: loading-wave 2s infinite ease-in-out;
}

@keyframes loading-wave {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 加载状态的条纹效果 */
.timeline-element-loading-stripes {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(25, 118, 210, 0.1) 10px,
    rgba(25, 118, 210, 0.1) 20px
  );
  animation: loading-stripes 2s infinite linear;
}

@keyframes loading-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40px 40px;
  }
}
