"use client";
import { Box } from "@mui/material";
import React from "react";
import { useDroppable } from "@dnd-kit/core";
import { TrackType } from "../../types";

// 轨道间隔组件的属性
interface TrackGapProps {
  id: string;
  beforeTrackId: string | null;
  afterTrackId: string | null;
  trackType: TrackType;
  isVisible?: boolean; // 新增可见性控制属性
}

// 轨道间隔组件
export const TrackGap: React.FC<TrackGapProps> = ({
  id,
  beforeTrackId,
  afterTrackId,
  trackType,
  isVisible = true, // 默认为可见
}) => {
  // 配置可放置区域
  const { setNodeRef, isOver } = useDroppable({
    id,
    data: {
      type: "gap",
      beforeTrackId,
      afterTrackId,
      trackType,
    },
  });

  return (
    <Box
      ref={setNodeRef}
      id={id}
      sx={{
        width: "100%",
        height: "6px", // 减小高度，保持精细的外观
        // my: 0.125,
        transition: "all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
        // borderRadius: 1,
        position: "relative",
        opacity: isVisible ? 1 : 0,
        pointerEvents: isVisible ? "auto" : "none",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        "&::before": {
          content: '""',
          position: "absolute",
          top: "50%",
          left: "10px", // 留出边距
          right: "10px",
          height: "1px",
          transform: "translateY(-50%)",
          backgroundColor: "transparent",
          transition: "all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
          //borderRadius: "1px",
        },
        "&::after": {
          content: '""',
          position: "absolute",
          top: "50%",
          left: "10px",
          right: "10px",
          height: "0px",
          transform: "translateY(-50%)",
          pointerEvents: "none",
          opacity: 0,
          transition: "all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
          // borderRadius: "2px",
          zIndex: 1,
        },
        "&:hover:not(.track-gap-highlight)::before": {
          // 悬停效果
          backgroundColor: "rgba(180, 180, 180, 0.4)",
          height: "1px",
        },
        "&.track-gap-highlight": {
          height: "6px",
          "&::before": {
            backgroundColor: "primary.main",
            height: "2px",
            boxShadow: "0 0 6px rgba(33, 150, 243, 0.6)",
          },
          "&::after": {
            opacity: 1,
            height: "3px",
            border: "1px dashed rgba(33, 150, 243, 0.8)",
            borderRadius: "2px",
            backgroundColor: "rgba(33, 150, 243, 0.1)",
          },
        },
        // 添加关键帧动画
        "@keyframes pulse": {
          "0%": {
            opacity: 0.6,
          },
          "50%": {
            opacity: 1,
          },
          "100%": {
            opacity: 0.6,
          },
        },
      }}
    />
  );
};
