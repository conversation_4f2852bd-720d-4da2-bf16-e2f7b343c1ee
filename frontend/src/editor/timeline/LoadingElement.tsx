import React from "react";
import { Box, alpha } from "@mui/material";
import { Image } from "@mui/icons-material";
import LoadingProgressBar from "./LoadingProgressBar";

interface LoadingElementProps {
  loadingState: {
    id: string;
    src: string;
    metadata?: any;
    dropTime: number;
    isLoading: boolean;
  };
  containerWidth: number;
  pixelsPerMs: number;
  currentTime: number;
}

const LoadingElement: React.FC<LoadingElementProps> = ({
  loadingState,
  containerWidth,
  pixelsPerMs,
  currentTime,
}) => {
  // 计算元素的位置和尺寸
  const startTime = loadingState.dropTime;
  const duration = 5000; // 默认5秒持续时间
  const endTime = startTime + duration;
  
  const elementLeft = (startTime / 1000) * pixelsPerMs;
  const elementWidth = Math.max((duration / 1000) * pixelsPerMs, 40);
  
  // 如果元素不在可视区域内，不渲染
  if (elementLeft + elementWidth < 0 || elementLeft > containerWidth) {
    return null;
  }

  return (
    <Box
      sx={{
        position: "absolute",
        left: `${elementLeft}px`,
        width: `${elementWidth}px`,
        height: "36px",
        backgroundColor: alpha("#1976d2", 0.1),
        border: "1px solid #1976d2",
        borderRadius: "4px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        overflow: "hidden",
        cursor: "default",
        zIndex: 1,
      }}
      className="timeline-image-loading"
    >
      {/* 元素内容 */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 0.5,
          color: "#1976d2",
          fontSize: "12px",
          fontWeight: 500,
          textOverflow: "ellipsis",
          overflow: "hidden",
          whiteSpace: "nowrap",
          px: 1,
        }}
      >
        <Image fontSize="small" />
        <span>加载中...</span>
      </Box>

      {/* 加载进度条 */}
      <LoadingProgressBar
        isLoading={true}
        progress={50}
        hasError={false}
        elementType="图片"
      />
    </Box>
  );
};

export default LoadingElement;
