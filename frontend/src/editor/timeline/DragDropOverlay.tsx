import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  useContext,
} from "react";
import { Box, Typography, styled } from "@mui/material";
import { Image as ImageIcon, VideoFile, AudioFile } from "@mui/icons-material";
import { StoreContext } from "../../store";
import { TIMELINE_CONSTANTS } from "../../utils/timeUtils";

interface DragDropOverlayProps {
  isVisible: boolean;
  dragData: any;
  mousePosition: { x: number; y: number };
  timelineRect: DOMRect | null;
  onPositionUpdate: (time: number, trackIndex: number) => void;
}

// 插入指示器组件
const InsertionIndicator = styled(Box)(({ theme }) => ({
  position: "absolute",
  width: "3px",
  height: "100%",
  backgroundColor: theme.palette.primary.main,
  borderRadius: "2px",
  boxShadow: `0 0 8px ${theme.palette.primary.main}`,
  zIndex: 1000,
  "&::before": {
    content: '""',
    position: "absolute",
    top: "-6px",
    left: "-6px",
    width: "15px",
    height: "15px",
    backgroundColor: theme.palette.primary.main,
    borderRadius: "50%",
    boxShadow: `0 0 8px ${theme.palette.primary.main}`,
  },
  "&::after": {
    content: '""',
    position: "absolute",
    bottom: "-6px",
    left: "-6px",
    width: "15px",
    height: "15px",
    backgroundColor: theme.palette.primary.main,
    borderRadius: "50%",
    boxShadow: `0 0 8px ${theme.palette.primary.main}`,
  },
}));

// 预览元素组件
const PreviewElement = styled(Box)(({ theme }) => ({
  position: "absolute",
  height: "40px",
  backgroundColor: "rgba(25, 118, 210, 0.3)",
  border: `2px dashed ${theme.palette.primary.main}`,
  borderRadius: "4px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: theme.palette.primary.main,
  fontSize: "12px",
  fontWeight: "bold",
  zIndex: 999,
  animation: "pulse 1.5s ease-in-out infinite",
  "@keyframes pulse": {
    "0%": {
      opacity: 0.6,
    },
    "50%": {
      opacity: 1,
    },
    "100%": {
      opacity: 0.6,
    },
  },
}));

// 时间显示组件
const TimeDisplay = styled(Box)(({ theme }) => ({
  position: "absolute",
  top: "-30px",
  left: "50%",
  transform: "translateX(-50%)",
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: "4px",
  padding: "4px 8px",
  fontSize: "12px",
  fontWeight: "bold",
  color: theme.palette.text.primary,
  boxShadow: theme.shadows[2],
  zIndex: 1001,
  whiteSpace: "nowrap",
}));

// 轨道高亮组件
const TrackHighlight = styled(Box)(({ theme }) => ({
  position: "absolute",
  width: "100%",
  height: "100%",
  backgroundColor: "rgba(25, 118, 210, 0.1)",
  border: `2px solid ${theme.palette.primary.main}`,
  borderRadius: "4px",
  zIndex: 998,
  animation: "glow 2s ease-in-out infinite",
  "@keyframes glow": {
    "0%": {
      boxShadow: `0 0 5px ${theme.palette.primary.main}`,
    },
    "50%": {
      boxShadow: `0 0 20px ${theme.palette.primary.main}`,
    },
    "100%": {
      boxShadow: `0 0 5px ${theme.palette.primary.main}`,
    },
  },
}));

export const DragDropOverlay: React.FC<DragDropOverlayProps> = ({
  isVisible,
  dragData,
  mousePosition,
  timelineRect,
  onPositionUpdate,
}) => {
  const store = useContext(StoreContext);
  const [insertionPosition, setInsertionPosition] = useState<{
    x: number;
    time: number;
    trackIndex: number;
  } | null>(null);
  const [previewElement, setPreviewElement] = useState<{
    x: number;
    y: number;
    width: number;
    trackIndex: number;
  } | null>(null);
  const [isNewTrack, setIsNewTrack] = useState(false);

  // 格式化时间显示
  const formatTime = (timeMs: number): string => {
    const totalSeconds = Math.floor(timeMs / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const milliseconds = Math.floor((timeMs % 1000) / 10);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}.${milliseconds.toString().padStart(2, "0")}`;
  };

  // 获取媒体类型图标
  const getMediaIcon = (type: string) => {
    switch (type) {
      case "image":
        return <ImageIcon sx={{ fontSize: 16, mr: 0.5 }} />;
      case "video":
        return <VideoFile sx={{ fontSize: 16, mr: 0.5 }} />;
      case "audio":
        return <AudioFile sx={{ fontSize: 16, mr: 0.5 }} />;
      default:
        return <ImageIcon sx={{ fontSize: 16, mr: 0.5 }} />;
    }
  };

  // 计算拖拽位置
  const calculateDragPosition = useCallback(() => {
    if (!timelineRect || !isVisible || !store) return;

    const { x, y } = mousePosition;
    const relativeX = x - timelineRect.left;
    const relativeY = y - timelineRect.top;

    // 使用与时间线相同的计算逻辑
    const { HANDLE_WIDTH } = TIMELINE_CONSTANTS;
    const effectiveWidth = timelineRect.width - HANDLE_WIDTH;
    const timePercent = Math.max(
      0,
      Math.min(1, (relativeX - HANDLE_WIDTH) / effectiveWidth)
    );

    // 计算实际时间位置
    const visibleStartTime = store.timelinePan.offsetX;
    const time = visibleStartTime + timePercent * store.timelineDisplayDuration;

    // 计算轨道索引（基于实际轨道高度）
    const trackHeight = 60; // 与实际轨道高度保持一致
    const trackIndex = Math.floor((relativeY - 30) / trackHeight); // 减去时间刻度的高度

    // 获取当前可用的轨道
    const elementType = dragData?.type === "image" ? "image" : "media";
    const trackType = "media"; // 图片使用media轨道类型
    const availableTracks = store.trackManager.tracks.filter(
      (track) => track.type === trackType
    );

    // 确定实际的轨道索引和是否需要创建新轨道
    const actualTrackIndex = Math.max(0, trackIndex);
    const needsNewTrack = actualTrackIndex >= availableTracks.length;
    setIsNewTrack(needsNewTrack);

    // 设置插入位置
    setInsertionPosition({
      x: relativeX,
      time,
      trackIndex: actualTrackIndex,
    });

    // 计算预览元素宽度（基于默认持续时间）
    const defaultDuration = 5000; // 5秒默认持续时间
    const elementWidth =
      (defaultDuration / store.timelineDisplayDuration) * effectiveWidth;
    const clampedWidth = Math.max(50, Math.min(200, elementWidth)); // 限制宽度范围

    // 设置预览元素位置
    const yPosition = actualTrackIndex * trackHeight + 35; // 加上时间刻度高度
    setPreviewElement({
      x: Math.max(HANDLE_WIDTH, relativeX - clampedWidth / 2),
      y: yPosition,
      width: clampedWidth,
      trackIndex: actualTrackIndex,
    });

    // 通知父组件位置更新
    onPositionUpdate(time, Math.max(0, trackIndex));
  }, [mousePosition, timelineRect, isVisible, onPositionUpdate, store]);

  useEffect(() => {
    calculateDragPosition();
  }, [calculateDragPosition]);

  if (!isVisible || !timelineRect || !insertionPosition) {
    return null;
  }

  return (
    <Box
      sx={{
        position: "fixed",
        top: timelineRect.top,
        left: timelineRect.left,
        width: timelineRect.width,
        height: timelineRect.height,
        pointerEvents: "none",
        zIndex: 1000,
      }}
    >
      {/* 轨道高亮 */}
      {previewElement && (
        <TrackHighlight
          sx={{
            top: previewElement.y - 5,
            left: 10,
            width: timelineRect.width - 20,
            height: 50,
            // 如果是新轨道，使用不同的样式
            backgroundColor: isNewTrack
              ? "rgba(76, 175, 80, 0.1)"
              : "rgba(25, 118, 210, 0.1)",
            borderColor: isNewTrack
              ? "rgba(76, 175, 80, 0.6)"
              : "rgba(25, 118, 210, 0.6)",
          }}
        />
      )}

      {/* 插入指示器 */}
      <InsertionIndicator
        sx={{
          left: insertionPosition.x,
          top: 0,
          backgroundColor: isNewTrack ? "#4caf50" : "#1976d2",
          boxShadow: isNewTrack ? "0 0 8px #4caf50" : "0 0 8px #1976d2",
          "&::before, &::after": {
            backgroundColor: isNewTrack ? "#4caf50" : "#1976d2",
            boxShadow: isNewTrack ? "0 0 8px #4caf50" : "0 0 8px #1976d2",
          },
        }}
      >
        {/* 时间显示 */}
        <TimeDisplay>
          {formatTime(insertionPosition.time)}
          {isNewTrack && (
            <Typography
              variant="caption"
              sx={{
                display: "block",
                color: "success.main",
                fontSize: "10px",
                fontWeight: "bold",
              }}
            >
              NEW TRACK
            </Typography>
          )}
        </TimeDisplay>
      </InsertionIndicator>

      {/* 预览元素 */}
      {previewElement && dragData && (
        <PreviewElement
          sx={{
            left: previewElement.x,
            top: previewElement.y,
            width: previewElement.width,
            backgroundColor: isNewTrack
              ? "rgba(76, 175, 80, 0.3)"
              : "rgba(25, 118, 210, 0.3)",
            borderColor: isNewTrack ? "#4caf50" : "#1976d2",
            color: isNewTrack ? "#4caf50" : "#1976d2",
          }}
        >
          {getMediaIcon(dragData.type)}
          <Typography variant="caption" sx={{ textTransform: "uppercase" }}>
            {dragData.type}
            {isNewTrack && " (NEW)"}
          </Typography>
        </PreviewElement>
      )}
    </Box>
  );
};
