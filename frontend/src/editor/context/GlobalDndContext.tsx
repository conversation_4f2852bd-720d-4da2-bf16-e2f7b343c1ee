import React, { useCallback, useContext, useState } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from "@dnd-kit/core";
import { Box } from "@mui/material";
import { StoreContext } from "../../store";
import { getUid } from "../../utils";

// 拖拽数据类型
interface ImageDragData {
  type: "image";
  src: string;
  imageId: string | number;
  metadata?: any;
  thumbnailSrc?: string; // 添加缩略图URL
}

interface GlobalDndContextProps {
  children: React.ReactNode;
}

export const GlobalDndContext: React.FC<GlobalDndContextProps> = ({
  children,
}) => {
  const store = useContext(StoreContext);
  const [activeItem, setActiveItem] = useState<ImageDragData | null>(null);

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // 减少激活距离，让拖拽更敏感
      },
    })
  );

  // 处理拖拽开始
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    if (active.data.current?.type === "image") {
      setActiveItem(active.data.current as ImageDragData);
    }
  }, []);

  // 处理拖拽悬停
  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;

    // 只处理从外部拖拽到时间线的情况
    if (
      active.data.current?.type === "image" &&
      over?.id === "timeline-drop-zone"
    ) {
      // 可以在这里添加视觉反馈
    }
  }, []);

  // 处理拖拽结束
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      // 清除活动项
      setActiveItem(null);

      // 检查是否是图片拖拽到时间线
      if (
        active.data.current?.type === "image" &&
        over?.id === "timeline-drop-zone"
      ) {
        const dragData = active.data.current as ImageDragData;

        // 简化处理：将图片添加到时间线的当前时间位置
        const dropTime = store.currentTimeInMs;

        // 创建图片元素
        const id = getUid();
        const imageElement = document.createElement("img");
        imageElement.src = dragData.src;
        imageElement.id = `image-${id}`;
        imageElement.style.display = "none";
        document.body.appendChild(imageElement);

        imageElement.onload = () => {
          // 添加图片元素到时间线
          store.addImageElement(imageElement, id, dragData.metadata);

          // 查找刚添加的元素
          const newElement = store.editorElements.find((el) => el.id === id);
          if (newElement) {
            // 设置元素的开始时间为当前时间位置
            const duration =
              newElement.timeFrame.end - newElement.timeFrame.start;
            store.updateEditorElementTimeFrame(
              newElement,
              {
                start: dropTime,
                end: dropTime + duration,
              },
              true
            );

            // 选中新添加的元素
            store.setSelectedElement(newElement);
          }
        };

        imageElement.onerror = () => {
          console.error("Failed to load dropped image");
          document.body.removeChild(imageElement);
        };
      }
    },
    [store]
  );

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      {children}
      <DragOverlay
        dropAnimation={{
          duration: 300,
          easing: "cubic-bezier(0.18, 0.67, 0.6, 1.22)",
        }}
      >
        {activeItem ? (
          <Box
            sx={{
              width: 140,
              height: 90,
              borderRadius: 3,
              overflow: "hidden",
              boxShadow: "0 12px 32px rgba(0,0,0,0.4)",
              border: "3px solid #1976d2",
              backgroundColor: "white",
              transform: "rotate(3deg) scale(1.05)",
              cursor: "grabbing",
              position: "relative",
              "&::after": {
                content: '"📷"',
                position: "absolute",
                top: -8,
                right: -8,
                width: 24,
                height: 24,
                backgroundColor: "#1976d2",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "12px",
                color: "white",
                border: "2px solid white",
                boxShadow: "0 2px 8px rgba(0,0,0,0.2)",
              },
            }}
          >
            <img
              src={activeItem.thumbnailSrc || activeItem.src}
              alt="Dragging"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
                pointerEvents: "none",
                filter: "brightness(1.1) contrast(1.1)",
              }}
            />
            <Box
              sx={{
                position: "absolute",
                bottom: 0,
                left: 0,
                right: 0,
                background: "linear-gradient(transparent, rgba(0,0,0,0.7))",
                color: "white",
                fontSize: "10px",
                padding: "4px 6px",
                textAlign: "center",
                fontWeight: "500",
              }}
            >
              拖拽到时间线
            </Box>
          </Box>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};
