import React, { useCallback, useContext, useState } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from "@dnd-kit/core";
import { Box } from "@mui/material";
import { StoreContext } from "../../store";
import { getUid } from "../../utils";

// 拖拽数据类型
interface ImageDragData {
  type: "image";
  src: string;
  imageId: string | number;
  metadata?: any;
  thumbnailSrc?: string; // 添加缩略图URL
}

interface GlobalDndContextProps {
  children: React.ReactNode;
}

export const GlobalDndContext: React.FC<GlobalDndContextProps> = ({
  children,
}) => {
  const store = useContext(StoreContext);
  const [activeItem, setActiveItem] = useState<ImageDragData | null>(null);

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // 减少激活距离，让拖拽更敏感
      },
    })
  );

  // 处理拖拽开始
  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      if (active.data.current?.type === "image") {
        setActiveItem(active.data.current as ImageDragData);

        // 添加全局拖拽样式类
        document.body.classList.add("global-image-dragging");

        // 如果正在播放，暂停播放
        if (store.playing) {
          store.setPlaying(false);
        }
      }
    },
    [store]
  );

  // 处理拖拽悬停
  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;

    // 只处理从外部拖拽到时间线的情况
    if (
      active.data.current?.type === "image" &&
      over?.id === "timeline-drop-zone"
    ) {
      // 可以在这里添加视觉反馈
    }
  }, []);

  // 处理拖拽结束
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      // 清除活动项和全局样式
      setActiveItem(null);
      document.body.classList.remove("global-image-dragging");

      // 检查是否是图片拖拽到时间线
      if (
        active.data.current?.type === "image" &&
        over?.id === "timeline-drop-zone"
      ) {
        const dragData = active.data.current as ImageDragData;

        // 简化处理：将图片添加到时间线的当前时间位置
        const dropTime = store.currentTimeInMs;

        // 创建图片元素
        const id = getUid();
        const imageElement = document.createElement("img");
        imageElement.src = dragData.src;
        imageElement.id = `image-${id}`;
        imageElement.style.display = "none";
        document.body.appendChild(imageElement);

        imageElement.onload = () => {
          // 添加图片元素到时间线
          store.addImageElement(imageElement, id, dragData.metadata);

          // 查找刚添加的元素
          const newElement = store.editorElements.find((el) => el.id === id);
          if (newElement) {
            // 设置元素的开始时间为当前时间位置
            const duration =
              newElement.timeFrame.end - newElement.timeFrame.start;
            store.updateEditorElementTimeFrame(
              newElement,
              {
                start: dropTime,
                end: dropTime + duration,
              },
              true
            );

            // 选中新添加的元素
            store.setSelectedElement(newElement);
          }
        };

        imageElement.onerror = () => {
          console.error("Failed to load dropped image");
          document.body.removeChild(imageElement);
        };
      }
    },
    [store]
  );

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      {children}
      <DragOverlay
        dropAnimation={{
          duration: 200,
          easing: "cubic-bezier(0.18, 0.67, 0.6, 1.22)",
        }}
      >
        {activeItem ? (
          <Box
            className="global-drag-overlay"
            sx={{
              width: 160,
              height: 40, // 时间线元素的高度
              borderRadius: 1,
              overflow: "hidden",
              boxShadow: "0 5px 15px rgba(0, 0, 0, 0.3)",
              border: "2px dashed rgba(33, 150, 243, 0.8)",
              backgroundColor: "rgba(33, 150, 243, 0.15)",
              cursor: "grabbing",
              position: "relative",
              transform: "translate3d(0, 0, 0) scale(1.02)",
              opacity: 0.9,
              zIndex: 1000,
              willChange: "transform, opacity, box-shadow",
              backfaceVisibility: "hidden",
              display: "flex",
              alignItems: "center",
              padding: "0 8px",
              gap: 1,
            }}
          >
            {/* 缩略图 */}
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: 1,
                overflow: "hidden",
                flexShrink: 0,
                border: "1px solid rgba(255,255,255,0.3)",
              }}
            >
              <img
                src={activeItem.thumbnailSrc || activeItem.src}
                alt="Dragging"
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  pointerEvents: "none",
                }}
              />
            </Box>

            {/* 文字信息 */}
            <Box
              sx={{
                flex: 1,
                color: "white",
                fontSize: "12px",
                fontWeight: "500",
                textShadow: "0 1px 2px rgba(0,0,0,0.5)",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              {activeItem.metadata?.name || "图片"}
            </Box>

            {/* 拖拽图标 */}
            <Box
              sx={{
                color: "white",
                fontSize: "14px",
                opacity: 0.8,
              }}
            >
              ⋮⋮
            </Box>
          </Box>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};
