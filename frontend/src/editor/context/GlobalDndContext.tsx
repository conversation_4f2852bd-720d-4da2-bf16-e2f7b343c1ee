import React, { useCallback, useContext, useMemo } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from "@dnd-kit/core";
import { StoreContext } from "../../store";
import { getUid } from "../../utils";
import {
  TIMELINE_CONSTANTS,
  getTimelineContainerWidth,
} from "../../utils/timeUtils";

// 拖拽数据类型
interface ImageDragData {
  type: "image";
  src: string;
  imageId: string | number;
  metadata?: any;
}

interface GlobalDndContextProps {
  children: React.ReactNode;
}

export const GlobalDndContext: React.FC<GlobalDndContextProps> = ({
  children,
}) => {
  const store = useContext(StoreContext);

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px移动距离后激活拖拽
      },
    })
  );

  // 处理拖拽开始
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    console.log("Global drag start:", active.id, active.data.current);
  }, []);

  // 处理拖拽悬停
  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;

    // 只处理从外部拖拽到时间线的情况
    if (
      active.data.current?.type === "image" &&
      over?.id === "timeline-drop-zone"
    ) {
      // 可以在这里添加视觉反馈
    }
  }, []);

  // 处理拖拽结束
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over, delta } = event;

      // 检查是否是图片拖拽到时间线
      if (
        active.data.current?.type === "image" &&
        over?.id === "timeline-drop-zone"
      ) {
        const dragData = active.data.current as ImageDragData;

        // 简化处理：将图片添加到时间线的当前时间位置
        const dropTime = store.currentTimeInMs;

        // 创建图片元素
        const id = getUid();
        const imageElement = document.createElement("img");
        imageElement.src = dragData.src;
        imageElement.id = `image-${id}`;
        imageElement.style.display = "none";
        document.body.appendChild(imageElement);

        imageElement.onload = () => {
          // 添加图片元素到时间线
          store.addImageElement(imageElement, id, dragData.metadata);

          // 查找刚添加的元素
          const newElement = store.editorElements.find((el) => el.id === id);
          if (newElement) {
            // 设置元素的开始时间为当前时间位置
            const duration =
              newElement.timeFrame.end - newElement.timeFrame.start;
            store.updateEditorElementTimeFrame(
              newElement,
              {
                start: dropTime,
                end: dropTime + duration,
              },
              true
            );

            // 选中新添加的元素
            store.setSelectedElement(newElement);
          }
        };

        imageElement.onerror = () => {
          console.error("Failed to load dropped image");
          document.body.removeChild(imageElement);
        };
      }
    },
    [store]
  );

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      {children}
      <DragOverlay>{/* 可以在这里添加拖拽预览 */}</DragOverlay>
    </DndContext>
  );
};
