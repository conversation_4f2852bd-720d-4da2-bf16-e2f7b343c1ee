import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";

// 全局错误处理器，用于捕获未处理的Promise错误
window.addEventListener("unhandledrejection", (event) => {
  // 检查是否是Wavesurfer相关的音频解码错误
  if (
    event.reason &&
    (event.reason.name === "EncodingError" ||
      event.reason.message?.includes("decode") ||
      event.reason.message?.includes("Unable to decode audio data") ||
      event.reason.message?.includes("Audio load timeout") ||
      event.reason.message?.includes("Audio format not supported"))
  ) {
    console.warn(
      "Caught audio processing error (handled gracefully):",
      event.reason.message || event.reason.name
    );
    // 阻止错误冒泡到控制台，因为我们已经在应用中优雅处理了
    event.preventDefault();
    return;
  }

  // 检查是否是网络相关错误
  if (
    event.reason &&
    (event.reason.message?.includes("fetch") ||
      event.reason.message?.includes("network") ||
      event.reason.message?.includes("CORS"))
  ) {
    console.warn(
      "Caught network error (handled gracefully):",
      event.reason.message
    );
    event.preventDefault();
    return;
  }

  // 对于其他错误，让它们正常处理
  console.error("Unhandled promise rejection:", event.reason);
});

const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
root.render(<App />);
